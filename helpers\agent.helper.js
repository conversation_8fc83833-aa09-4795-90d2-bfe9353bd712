const logger = require('../config/logger');
const path = require("path");
const { transformWithDTL } = require("./others.helpers");
/**
 * Load mapping configuration for agent
 * @param {string} mappingName - Name of the mapping file
 * @returns {Object} Mapping configuration
 */
function loadMappingConfig(mappingName) {
  try {
    const mappingConfig = require(path.join(process.cwd(), `mappings/${mappingName}.mapping.json`));
    return mappingConfig;
  } catch (error) {
    logger.error(`[Agent Helper] Failed to load mapping config ${mappingName}:`, error);
    throw new Error(`Mapping configuration '${mappingName}' not found`);
  }
}

/**
 * Validate agent configuration
 * @param {Object} agent - Agent configuration
 * @returns {boolean} Validation result
 */
function validateAgentConfig(agent) {
  const requiredFields = ['name', 'type', 'source', 'queue', 'handler'];

  for (const field of requiredFields) {
    if (!agent[field]) {
      logger.error(`[Agent Helper] Agent validation failed: missing ${field}`);
      return false;
    }
  }

  if (!['Inbound', 'Outbound'].includes(agent.type)) {
    logger.error(`[Agent Helper] Agent validation failed: invalid type ${agent.type}`);
    return false;
  }

  return true;
}

/**
 * Log agent activity with consistent format
 * @param {string} agentName - Agent name
 * @param {string} activity - Activity description
 * @param {Object} metadata - Additional metadata
 */
function logAgentActivity(agentName, activity, metadata = {}) {
  logger.info(`[Agent: ${agentName}] ${activity}`, {
    agentName,
    timestamp: new Date().toISOString(),
    ...metadata
  });
}

function transform_CSVHandler(input, dtlTemplate) {
  try {
    const transformed = transformWithDTL(input, dtlTemplate);
    
    //convert undefined values to empty string
    Object.values(transformed).forEach(record => {
      for (const [key, value] of Object.entries(record)) {
        if (value === undefined) {
          record[key] = '';
        }
      }
    });

    return Object.values(transformed)[0];
  } catch (err) {
    throw new Error(`CSV Transformation failed: ${err.message}`);
  }
}



module.exports = {
  loadMappingConfig,
  validateAgentConfig,
  logAgentActivity,
  transform_CSVHandler
};
