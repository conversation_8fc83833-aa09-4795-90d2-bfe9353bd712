"use strict";
const { v4: uuidv4 } = require("uuid");
const { faker } = require("@faker-js/faker");

module.exports = {
  up: async (queryInterface, Sequelize) => {
    const transaction = await queryInterface.sequelize.transaction();
    try {
      // Step 1: Create a facility and get its ID
      const facilityId = uuidv4();
      const facility = {
        facility_id: facilityId,
        name: "Test1 Medical1 Center1",
        facility_code: "TMC001",
        facility_type: 1,
        phone: "******-0100",
        email: "<EMAIL>",
        status: 0,
        created_at: new Date(),
        updated_at: new Date(),
      };
      await queryInterface.bulkInsert("facility", [facility], { transaction });
      console.log(`Created facility with ID: ${facilityId}`);

      // Step 2: Create a host identity and get its ID
      const hostId = uuidv4();
      const hostIdentity = {
        identity_id: hostId,
        first_name: "<PERSON><PERSON> <PERSON>",
        last_name: "<PERSON>",
        email: "<EMAIL>",
        eid: "EMP001",
        mobile: "******-0101",
        facility_id: facilityId,
        status: 0,
        identity_type: 0,
        start_date: '2024-01-01',
        end_date: '2025-12-31',
        created_at: new Date(),
        updated_at: new Date(),
      };
      await queryInterface.bulkInsert("identity", [hostIdentity], { transaction });
      console.log(`Created host with ID: ${hostId}`);

      // Step 3: Create an escort identity and get its ID
      const escortId = uuidv4();
      const escortIdentity = {
        identity_id: escortId,
        first_name: "Jane",
        last_name: "Wilson",
        email: "<EMAIL>",
        eid: "EMP002",
        mobile: "******-0102",
        facility_id: facilityId,
        status: 0,
        identity_type: 0,
        start_date: '2024-06-01',
        end_date: '2026-05-31',
        created_at: new Date(),
        updated_at: new Date(),
      };
      await queryInterface.bulkInsert("identity", [escortIdentity], { transaction });
      console.log(`Created escort with ID: ${escortId}`);

      // Step 4: Create 10 guests
      const guests = Array.from({ length: 10 }).map(() => ({
        guest_id: uuidv4(),
        first_name: faker.person.firstName(),
        last_name: faker.person.lastName(),
        date_of_birth: faker.date.past({ years: 50, refDate: new Date(2000, 0, 1) }),
        email: faker.internet.email(),
        mobile_phone: faker.phone.number().slice(0, 20),
        image: null,
        created_at: new Date(),
        updated_at: new Date(),
      }));

      await queryInterface.bulkInsert("guest", guests, { transaction });

      // Step 5: Create 5 visits using the created facility, host, and escort
      const timeDistribution = [
        "11:00:00", // Visit 1
        "10:00:00", // Visit 2  
        "12:00:00", // Visit 3
        "11:00:00", // Visit 4
        "12:30:00"  // Visit 5
      ];

      const visits = Array.from({ length: 5 }).map((_, index) => ({
        visit_id: uuidv4(),
        title: `Test Visit ${index + 1}`,
        category: index + 1, // Simple numeric category
        type: index % 2, // Alternating 0 and 1
        facility_id: facilityId,
        host_id: hostId,
        escort_id: escortId,
        start_date: new Date(Date.now() + (index * 7 * 24 * 60 * 60 * 1000)), // One week apart
        end_date: new Date(Date.now() + ((index + 1) * 7 * 24 * 60 * 60 * 1000)),
        start_time: timeDistribution[index],
        duration: 60 + (index * 30), // Increasing duration
        status: 0, // Active
        created_at: new Date(),
        updated_at: new Date(),
      }));

      await queryInterface.bulkInsert("visit", visits, { transaction });

      // Step 6: Create guest_visit relationships with varied guest_status
      const guestVisits = [];
      const statusDistribution = [
        ...Array(3).fill(0), // 3 rows with status 0 (Invited)
        ...Array(3).fill(1), // 3 rows with status 1 (Checked-in)
        ...Array(3).fill(2), // 3 rows with status 2 (Checked-out)
        ...Array(1).fill(3)  // 1 row with status 3 (Denied)
      ];

      visits.forEach((visit, visitIndex) => {
        // Each visit has 2 guests
        const selectedGuests = faker.helpers.arrayElements(guests, 2);
        
        selectedGuests.forEach((guest, guestIndex) => {
          // Calculate index for status distribution (2 guests per visit)
          const statusIndex = (visitIndex * 2 + guestIndex) % statusDistribution.length;
          const guestStatus = statusDistribution[statusIndex];
          
          // Generate check-in/out times based on status
          let checkInTime = null;
          let checkOutTime = null;
          
          if (guestStatus >= 1) { // Checked-in or Checked-out
            checkInTime = new Date(Date.now() - faker.number.int({ min: 1, max: 8 }) * 60 * 60 * 1000);
          }
          
          if (guestStatus === 2) { // Checked-out
            checkOutTime = new Date(checkInTime.getTime() + faker.number.int({ min: 1, max: 4 }) * 60 * 60 * 1000);
          }
          
          guestVisits.push({
            guest_visit_id: uuidv4(),
            guest_id: guest.guest_id,
            visit_id: visit.visit_id,
            guest_status: guestStatus,
            guest_pin: faker.string.numeric(6),
            check_in_time: checkInTime,  // Date object or null
            check_out_time: checkOutTime, // Date object or null
            created_at: new Date(),
            updated_at: new Date(),
          });
        });
      });

      await queryInterface.bulkInsert("guest_visit", guestVisits, { transaction });
      
      console.log(`Created ${guests.length} guests, ${visits.length} visits, and ${guestVisits.length} guest-visit relationships`);
      console.log(`Use these IDs for testing:`);
      console.log(`Facility ID: ${facilityId}`);
      console.log(`Host ID: ${hostId}`);
      console.log(`Escort ID: ${escortId}`);
      
      await transaction.commit();
      return { facilityId, hostId, escortId };
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  },

  down: async (queryInterface, Sequelize) => {
    const transaction = await queryInterface.sequelize.transaction();
    try {
      // Get the facility IDs created by this seeder
      const [facilities] = await queryInterface.sequelize.query(
        "SELECT facility_id FROM facility WHERE name = 'Test1 Medical1 Center1'",
        { transaction }
      );
      
      if (facilities.length > 0) {
        const facilityIds = facilities.map(f => f.facility_id);
        
        // Delete guest_visits for visits in these facilities
        await queryInterface.sequelize.query(
          `DELETE FROM guest_visit WHERE visit_id IN 
           (SELECT visit_id FROM visit WHERE facility_id IN ('${facilityIds.join("','")}'))`,
          { transaction }
        );
        
        // Delete visits in these facilities
        await queryInterface.bulkDelete("visit", {
          facility_id: facilityIds
        }, { transaction });
        
        // Delete identities in these facilities
        await queryInterface.bulkDelete("identity", {
          facility_id: facilityIds
        }, { transaction });
        
        // Delete the facilities
        await queryInterface.bulkDelete("facility", {
          facility_id: facilityIds
        }, { transaction });
      }
      
      await transaction.commit();
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  },
};
